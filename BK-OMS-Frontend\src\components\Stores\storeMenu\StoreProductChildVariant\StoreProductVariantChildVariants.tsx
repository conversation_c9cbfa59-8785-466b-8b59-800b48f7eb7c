import { useCallback, useEffect, useState } from "react";
import {
  Button,
  Form,
  Input,
  InputNumber,
  Modal,
  Popover,
  Select,
  message,
} from "antd";
import { useParams } from "react-router-dom";
import { axiosInstance } from "../../../../apiCalls";
import { CheckOutlined, CloseOutlined, EditOutlined } from "@ant-design/icons";
import { debounce } from "lodash";
import "../../../Products/Products.css";
import DataTable from "../../../UI/DataTable/DataTable";

// const { Link } = Typography;

export interface ChildVariant {
  id: number;
  child_pos: number;
  child_name: string;
  section: string;
  position: number;
  parent: number;
  child: number;
  is_active: boolean;
}

const StoreProductVariantChildVariants: React.FC = () => {
  const { id, storeId } = useParams();
  const [childVarinats, setChildVarinats] = useState<ChildVariant[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [search, setSearch] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [productVariants, setProductVariants] = useState<any[]>([]);
  const [productVariantSearch, setProductVariantSearch] = useState("");
  const [productVariantPopoverVisible, setProductVariantPopoverVisible] =
    useState(false);
  const [intialStage, setIntialStage] = useState(false);
  const [editingId, setEditingId] = useState(0);
  const [section, setSection] = useState("");
  const [selectedProductVariantId, setSelectedProductVariantId] =
    useState<number>(0);
  const [editingVariant, setEditingVariant] = useState<ChildVariant | null>(
    null
  );

  const updateChildVariant = useCallback(
    async (payload: Partial<ChildVariant>) => {
      setLoading(true);
      try {
        const response = await axiosInstance.patch(
          "/api/menu/v2/store-product-childvariant-update/",
          payload
        );
        if (response.status === 200) {
          message.success("Updated successfully");
          await getChildVarinats(search);
          setIsEditing(false);
          setEditingVariant(null);
          return true;
        }
        message.error("Update failed");
        return false;
      } catch (error) {
        message.error("Update failed");
        return false;
      } finally {
        setLoading(false);
      }
    },
    [search]
  );

  // Fetch child variants
  const getChildVarinats = useCallback(
    async (search: string) => {
      setLoading(true);
      try {
        const response = await axiosInstance.get(
          `api/menu/v2/store-product-childvariant/?product_variant_id=${id}`,
          { params: { search } }
        );
        if (response.status === 200) {
          setChildVarinats(response.data);
        } else {
          message.error("Error fetching Child Varinats");
          setChildVarinats([]);
        }
      } catch (error) {
        message.error("Error fetching Child Varinats");
      } finally {
        setLoading(false);
      }
    },
    [id]
  );

  // Base product search
  const fetchBaseProducts = useCallback(
    debounce(async (searchText: string) => {
      if (searchText.length >= 3) {
        try {
          const response = await axiosInstance.get(
            `/api/menu/v2/list-store-product-variants/${storeId}/?search=${searchText}`
          );
          if (response.status === 200) {
            setProductVariants(response.data);
            setProductVariantPopoverVisible(response.data.length > 0);
          }
        } catch (error) {
          console.error("Error fetching base products:", error);
        }
      } else {
        setProductVariants([]);
        setProductVariantPopoverVisible(false);
      }
    }, 500),
    [storeId]
  );

  useEffect(() => {
    getChildVarinats(search);
  }, [search]);

  const handleStatusChange = (
    childVariant: ChildVariant,
    newState: boolean
  ) => {
    Modal.confirm({
      title: newState ? "Activate Status" : "Deactivate Status",
      content: `Are you sure you want to ${
        newState ? "activate" : "deactivate"
      } this status?`,
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: async () => {
        await updateChildVariant({ ...childVariant, is_active: newState });
      },
    });
  };

  const handleEditSubmit = async (values: ChildVariant) => {
    if (!editingVariant) return;
    const payload = {
      ...editingVariant,
      ...values,
      child: selectedProductVariantId,
      id: editingId,
    };
    await updateChildVariant(payload);
  };

  // Delete logic
  const showDeleteConfirm = (id: number) => {
    setDeleteId(id);
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    if (!deleteId) return;
    setLoading(true);
    try {
      const response = await axiosInstance.delete(
        `/api/menu/v2/store-product-childvariant-update/?id=${deleteId}`,
        { data: { id: deleteId } }
      );
      if (response.status === 204) {
        message.success("Child variant deleted successfully");
        getChildVarinats(search);
      } else {
        message.error("Failed to delete Child variant");
      }
    } catch (error) {
      message.error("Failed to delete Child variant");
    } finally {
      setIsDeleteModalOpen(false);
      setDeleteId(null);
      setLoading(false);
    }
  };

  const columns = [
    {
      title: "POS Number",
      dataIndex: "child_pos",
      key: "child_pos",
      width: "10%",
    },
    {
      title: "Child Name",
      dataIndex: "child_name",
      key: "child_name",
      width: "25%",
    },
    {
      title: "Section",
      dataIndex: "section",
      key: "section",
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
    },
    // {
    //   title: "Parent",
    //   dataIndex: "parent",
    //   key: "parent",
    // },
    {
      title: "Child",
      dataIndex: "child",
      key: "child",
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      width: "15%",
      render: (isActive: boolean, record: ChildVariant) => (
        <div
          className={`switch-button ${isActive ? "checked" : ""}`}
          onClick={() => handleStatusChange(record, !isActive)}
        >
          <span className="switch-label">
            {isActive ? <CheckOutlined /> : <CloseOutlined />}
          </span>
          <div className="switch-handle" />
        </div>
      ),
    },
    {
      title: "Edit",
      key: "edit",
      render: (_: any, record: ChildVariant) => (
        <Button
          type="link"
          onClick={() => {
            setEditingVariant(record);
            setIsEditing(true);
            setProductVariantSearch(record.child_name);
            setSection(record.section);
            setSelectedProductVariantId(record.child);
            setEditingId(record.id);
          }}
        >
          <EditOutlined className="btn-edit-pencil" />
        </Button>
      ),
    },
    {
      title: "Delete",
      key: "delete",
      render: (_: any, record: ChildVariant) => (
        <Button
          type="primary"
          danger
          onClick={() => showDeleteConfirm(record.id)}
        >
          Delete
        </Button>
      ),
    },
  ];

  useEffect(() => {
    if (productVariantSearch.length >= 3) {
      fetchBaseProducts(productVariantSearch);
      setProductVariantPopoverVisible(true);
    } else {
      setProductVariantPopoverVisible(false);
    }
  }, [productVariantSearch, fetchBaseProducts]);

  return (
    <div>
      <div className="main-dashboard-buttons"></div>
      <div className="container product-card-banner...........................">
        <div className="header products-headers">
          <div className="title">Child Varinats </div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            <div className="search-container">
              <div className="button-serachs">
                <input
                  className="search-text"
                  placeholder="Search by Name"
                  onChange={(e) => setSearch(e.target.value)}
                />
                <button onClick={() => getChildVarinats(search)}>Search</button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="pt-4 mt-4">
        <DataTable
          columns={columns}
          dataSource={childVarinats.map((childvariant) => ({
            ...childvariant,
            key: childvariant.id,
          }))}
          loading={loading}
        />
      </div>
      <Modal
        title="Edit Child Variant"
        open={isEditing}
        destroyOnClose
        onCancel={() => setIsEditing(false)}
        footer={null}
      >
        {editingVariant && (
          <Form
            layout="vertical"
            initialValues={{
              child_name: editingVariant.child_name,
              section: editingVariant.section,
              position: editingVariant.position,
            }}
            onFinish={handleEditSubmit}
          >
            <Form.Item label="Search Base Product">
              <Popover
                content={
                  <ul className="max-h-48 overflow-y-auto">
                   {(Array.isArray(productVariants) ? productVariants : []).map((item) => (
                      <li
                        key={item.id}
                        onClick={() => {
                          setSelectedProductVariantId(item.id);
                          setProductVariantSearch(item.variant_name);
                          setProductVariantPopoverVisible(false);
                        }}
                        className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                      >
                        {item.variant_name}
                        {`(${item.id})`}
                      </li>
                    ))}
                  </ul>
                }
                trigger="click"
                open={
                  productVariantPopoverVisible &&
                  intialStage &&
                  productVariantSearch.length >= 3
                }
              >
                <Input
                  placeholder="Type to search..."
                  value={productVariantSearch}
                  onChange={(e) => {
                    setProductVariantSearch(e.target.value);
                    setIntialStage(true);
                  }}
                />
              </Popover>
            </Form.Item>
            <Form.Item
              label="Section"
              name="section"
              initialValue={section}
              rules={[{ message: "Please input section" }]}
            >
              <Select placeholder="Select a product type">
                <Select.Option value="choose_drink">choose_drink</Select.Option>
                <Select.Option value="customize">customize</Select.Option>
                <Select.Option value="choose_side">choose_side</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              label="Position"
              name="position"
              rules={[{ required: true, message: "Please input position" }]}
            >
              <InputNumber style={{ width: "100%" }} />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit" block>
                Save Changes
              </Button>
            </Form.Item>
          </Form>
        )}
      </Modal>
      <Modal
        title="Confirm Deletion"
        open={isDeleteModalOpen}
        onOk={handleDelete}
        onCancel={() => setIsDeleteModalOpen(false)}
        okText="Delete"
        okType="danger"
        cancelText="Cancel"
      >
        <p>Are you sure you want to delete this Child variant?</p>
      </Modal>
    </div>
  );
};

export default StoreProductVariantChildVariants;
