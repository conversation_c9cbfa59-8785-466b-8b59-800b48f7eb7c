import { useCallback, useEffect, useState } from "react";
import { StoreDataProps } from "../../types";
import "../Products/Products.css";
import { axiosInstance } from "../../apiCalls";
import SearchBox from "../UI/SearchBox";
import DataTable from "../UI/DataTable/DataTable";
import Link from "../UI/Link/index";
import Button from "../UI/Button";

export interface Stories {
  objects: StoreDataProps[];
  page_size: number;
  current_page: number;
  total_pages: number;
  next_page: number | null;
  previous_page: number | null;
  total_count: number;
}

const StoreList: React.FC = () => {
  //const navigate = useNavigate();

  const [stories, setStories] = useState<Stories | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [search, setSearch] = useState("");

  const getStories = async (page: number, search: string) => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(`api/stores/`, {
        params: { page, search },
      });

      if (response.status === 200) {
        setStories(response.data);
        setLoading(false);
      } else {
        console.log("Error fetching menu data", response.status);
        setStories(null);
        setLoading(false);
      }
    } catch (error) {
      console.error("Error fetching menu data is", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    getStories(currentPage, search);
  }, [currentPage]);

  const handleSearch = useCallback(() => {
    setCurrentPage(1);
    getStories(1, search);
  }, [search, getStories, setCurrentPage]);

  const handleClear = useCallback(() => {
    setSearch("");
    setCurrentPage(1);
    getStories(1, "");
  }, [getStories, setCurrentPage, setSearch]);

  const columns = [
    {
      title: "Code",
      width: "15%",
      dataIndex: "code",
      key: "code",
      fixed: "left" as "left",
      render: (text: string, record: StoreDataProps) => (
        // <span onClick={() => navigate(`./${record.id}`)}>
        //   <Link>{text}</Link>
        // </span>
        <>
          {text ? (
            <Link
              className="common-link text-decoration-none"
              to={`./${record.id}`}
            >
              {text}
            </Link>
          ) : (
            "-"
          )}
        </>
      ),
    },
    {
      title: "POS Number",
      width: "15%",
      dataIndex: "ato_id",
      key: "ato_id",
    },
    {
      title: "Name",
      width: "15%",
      dataIndex: "name",
      key: "name",
    },

    {
      title: "Status",

      dataIndex: "is_active",
      key: "is_active",
      width: "15%",
      render: (isActive: boolean) => (
        <div>{isActive ? "Active" : "In Active"}</div>
      ),
    },
  ];

  const data: StoreDataProps[] =
    stories?.objects?.map((store: StoreDataProps) => ({
      key: store.id,
      id: store.id,
      name: store.name || "",
      code: store.code || "",
      timezone: store.timezone || 0,
      address: store.address || "",
      phone: store.phone || "",
      postal_code: store.postal_code || "",
      latitude: store.latitude || 0,
      longitude: store.longitude || 0,
      tax_percentage: store.tax_percentage || 0,
      can_accept_delivery_order: store.can_accept_delivery_order || false,
      is_active: store.is_active || false,
      coverage_type: store.coverage_type || "",
      third_party_id: store.third_party_id || "",
      take_away_charge: store.take_away_charge || 0,
      is_cash_payment_available: store.is_cash_payment_available || false,
      is_card_payment_available: store.is_card_payment_available || false,
      is_qr_code_payment_available: store.is_qr_code_payment_available || false,
      business: store.business || 0,
      store_config: store.store_config || null,
      banner_config: store.banner_config || null,
      ato_id: store.ato_id,
    })) || [];

  return (
    <div>
      <div className="main-dashboard-buttons">
        <Link to={`./addstore`}>
          <Button className="typography"> + Add New </Button>
        </Link>
      </div>
      <div className="container product-card-banner...........................">
        <div className="header products-headers">
          <div className="title">STORES</div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            {/* <div className="search-container">
              <div className="button-serachs">
                <input
                  className="search-text"
                  placeholder="Code or Name"
                  onChange={(e) => setSearch(e.target.value)}
                />
                <button onClick={() => getStories(currentPage, search)}>
                  Search
                </button>
              </div>
            </div> */}
            <div className="d-flex flex-wrap">
              <SearchBox
                value={search}
                onChange={setSearch}
                onSearch={() => handleSearch()}
                onClear={() => handleClear()}
                placeholder="Enter Code or Name"
              />
            </div>
            {/* <input className="search-text" placeholder="Search" /> */}
          </div>
        </div>
      </div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title"></div>
      <div className="pt-4 mt-4">
        <DataTable<StoreDataProps>
          columns={columns}
          dataSource={data}
          loading={loading}
          pagination={{
            current: currentPage,
            total: stories?.total_count || 0,
            pageSize: 10,
            showSizeChanger: false,
            onChange: (page) => setCurrentPage(page),
          }}
          scroll={{ x: 1100, y: 700 }}
        />
        ,
      </div>
    </div>
  );
};

export default StoreList;
