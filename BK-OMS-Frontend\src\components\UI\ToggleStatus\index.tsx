import { FC, memo, useCallback } from "react";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";

interface StatusToggleProps {
  isActive: boolean;
  id: number;
  onToggle: (id: number, newStatus: boolean) => void;
  className?: string;
}

const StatusToggle: FC<StatusToggleProps> = memo(
  ({ isActive, id, onToggle, className = "" }) => {
    const handleClick = useCallback(() => {
      onToggle(id, !isActive);
    }, [id, isActive, onToggle]);

    return (
      <div className={`d-flex ${className}`}>
        <div
          className={`switch-button ${isActive ? "checked" : ""}`}
          onClick={handleClick}
        >
          <span className="switch-label">
            {isActive ? <CheckOutlined /> : <CloseOutlined />}
          </span>
          <div className="switch-handle"></div>
        </div>
      </div>
    );
  }
);

export default StatusToggle;
