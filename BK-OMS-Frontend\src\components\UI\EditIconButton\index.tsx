// components/EditIconButton.tsx
import React from "react";
import { EditOutlined } from "@ant-design/icons";

// Define props interface
interface EditIconButtonProps {
  onClick?: () => void;
  className?: string;
  title?: string;
  style?: React.CSSProperties;
}

// Memoized reusable component
const EditIconButton: React.FC<EditIconButtonProps> = React.memo(
  ({ onClick, className = "btn-edit-pencil", title = "Edit", style }) => {
    return (
      <EditOutlined
        className={className}
        onClick={onClick}
        title={title}
        style={style}
      />
    );
  }
);

export default EditIconButton;
