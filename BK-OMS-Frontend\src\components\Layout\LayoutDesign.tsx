import React, { useEffect, useState } from "react";
import {
  Layout,
  Menu,
  theme,
  MenuProps,
  notification,
  message,
  Dropdown,
  Avatar,
} from "antd";
import {
  ShopOutlined,
  MenuOutlined,
  LogoutOutlined,
  ContainerOutlined,
  BookOutlined,
  AppstoreOutlined,
  AlignLeftOutlined,
  ProductOutlined,
  UserOutlined,
  DownOutlined,
} from "@ant-design/icons";
import { useNavigate, useLocation, Outlet } from "react-router-dom";
import logo from "../../assets/0a710658904456de435db5196ca6cfad.png";
import "./Layout.css";
import Cookies from "js-cookie";
import { Spinner } from "react-bootstrap";
import { axiosInstance } from "../../apiCalls";
import Link from "../UI/Link";

const { Header, Content, Sider } = Layout;

const sideNavBarContent = [
  {
    id: "1",
    label: "Products",
    icon: <ProductOutlined />,
    subData: ["Products", "Variants"],
  },
  {
    id: "2",
    label: "Stores",
    icon: <ShopOutlined />,
    subData: ["Stores", "Store Groups"],
  },
  {
    id: "3",
    label: "Modifiers",
    icon: <AlignLeftOutlined />,
    subData: ["Modifiers", "Modifiers Groups"],
  },
  {
    id: "4",
    label: "Orders",
    icon: <ContainerOutlined />,
    subData: ["Orders"],
  },
  {
    id: "5",
    label: "Categories",
    icon: <AppstoreOutlined />,
  },
  // {
  //   id: "6",
  //   icon: <CopyOutlined />,
  //   label: "Menu Copy",
  // },
  // {
  //   id: "7",
  //   icon: <SwitcherOutlined />,
  //   label: "Generate Menu",
  // },
  {
    id: "6",
    icon: <BookOutlined />,
    label: "Menu Management",
    subData: [
      "Menu Copy",
      // "Generate Menu",
      // "Menu Push",
      "Bulk Menu Push to Stores",
    ],
  },
];

// const items2 = sideNavBarContent.map((item) => ({
//   key: item.id,
//   label: item.label,
//   icon: item.icon,
//   children: item.subData
//     ? item.subData.map((subItem, index) => ({
//         key: `${item.id}-${index}`,
//         label: subItem,
//       }))
//     : undefined,
// }));
const items2 = sideNavBarContent.map((item) => {
  const basePath = `/${item.label.toLowerCase().replace(/\s+/g, "-")}`;

  return {
    key: item.id,
    icon: item.icon,
    label: !item.subData ? <Link to={basePath}>{item.label}</Link> : item.label,
    children: item.subData
      ? item.subData.map((subItem, index) => {
          const subPath = `/${subItem.toLowerCase().replace(/\s+/g, "-")}`;
          return {
            key: `${item.id}-${index}`,
            label: <Link to={subPath}>{subItem}</Link>,
          };
        })
      : undefined,
  };
});

const LayoutDesign: React.FC = () => {
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);

  const [collapsed, setCollapsed] = useState(false);
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [selectedKey, setSelectedKey] = useState<string>("");
  const [visible, setVisible] = useState(false);

  const getMenuKeyFromPath = (path: string) => {
    const cleanedPath = path
      .replace(/^\/+/, "")
      .replace(/-/g, " ")
      .toLowerCase();

    for (let item of sideNavBarContent) {
      if (item.subData) {
        for (let i = 0; i < item.subData.length; i++) {
          const subPath = item.subData[i].toLowerCase();
          if (cleanedPath === subPath.toLowerCase()) {
            return { selected: `${item.id}-${i}`, open: item.id };
          }
        }
      } else if (item.label.toLowerCase() === cleanedPath) {
        return { selected: item.id, open: item.id };
      }
    }
    return { selected: "", open: "" };
  };

  useEffect(() => {
    checkToken();

    const { selected, open } = getMenuKeyFromPath(location.pathname);
    setSelectedKey(selected);
    if (open) setOpenKeys([open]);
  }, [location.pathname]);

  const handleMenuClick = (menuInfo: any) => {
    const [mainId, subIndex] = menuInfo.key.split("-");
    const mainItem = sideNavBarContent.find((item) => item.id === mainId);
    if (mainItem) {
      if (subIndex && mainItem.subData) {
        const subLabel = mainItem.subData[parseInt(subIndex)];
        if (subLabel) {
          navigate(`/${subLabel.toLowerCase().replace(/\s+/g, "-")}`);
        }
      } else {
        navigate(`/${mainItem.label.toLowerCase().replace(/\s+/g, "-")}`);
      }
    }
  };

  const handleOpenChange = (keys: string[]) => {
    setOpenKeys(keys);
  };

  // const logout = async () => {
  //   try {
  //     const refreshToken = Cookies.get("refreshToken");
  //     if (!refreshToken) return;

  //     Cookies.remove("token");
  //     Cookies.remove("refreshToken");
  //     navigate("/");
  //   } catch (error: any) {
  //     console.error("Error during logout:", error);
  //     alert("Failed to logout. Please try again.");
  //   }
  // };
  const handleLogout = async () => {
    try {
      const refreshToken = Cookies.get("refreshToken");

      if (!refreshToken) {
        console.warn("Refresh token not found, skipping logout API call.");
        return;
      }
      setLoading(true);
      const response = await axiosInstance.post(
        "api/accounts/logout/",
        { refresh: refreshToken },
        { headers: { "Content-Type": "application/json" } }
      );

      if (response.status === 200) {
        Cookies.remove("token");
        Cookies.remove("refreshToken");
        // Cookies.remove("username");
        // Cookies.remove("first_name");
        // Cookies.remove("last_name");

        window.location.pathname = "/";
        notification.success({
          message: "Logout successful",
          description: response.data.message,
        });
      } else if (response.status === 401) {
        Cookies.remove("token");
        Cookies.remove("refreshToken");
        window.location.pathname = "/";
        notification.success({
          message: "Logout successful",
        });
      } else {
        notification.error({
          message: "Logout failed",
          description: response.data.message,
        });
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        message.error(error.message || "Failed to logout. Please try again.");
      } else {
        message.error("An unexpected error occurred. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };
  const menuItems: MenuProps["items"] = [
    {
      key: "logout",
      label: (
        <span className="logout-text">
          {loading ? <Spinner className="me-2" /> : null} Logout
        </span>
      ),
      icon: <LogoutOutlined className="logout-icon" />,
      onClick: handleLogout,
    },
  ];
  // const logout=()=>{
  //   Modal.confirm({
  //     title: "Are you sure you want to logout",

  //     okText: "Yes",
  //     cancelText: "No",
  //     className: "custom-modal",
  //     okButtonProps: { className: "custom-modal-ok-button" },
  //     cancelButtonProps: { className: "custom-modal-cancel-button" },
  //     onOk: async () => {
  //       try {
  //             const refreshToken = Cookies.get("refreshToken");
  //             if (!refreshToken) return;

  //             Cookies.remove("token");
  //             Cookies.remove("refreshToken");
  //             navigate("/");
  //           } catch (error: any) {
  //             console.error("Error during logout:", error);
  //             alert("Failed to logout. Please try again.");
  //           }
  //         }
  //   });
  // }

  const checkToken = () => {
    const token = Cookies.get("token");
    const refreshToken = Cookies.get("refreshToken");
    if (!token || !refreshToken) {
      navigate("/");
    }
  };

  return (
    <Layout className="layout">
      <Header className="layout-header">
        <div className="header-section-s">
          <div className="logo-section-s">
            <MenuOutlined
              className="menu-icon"
              onClick={() => setCollapsed(!collapsed)}
            />
            <img src={logo} alt="Logo" className="oms-logo" />
          </div>
          <div></div>
          <div>
            {/* <Button className="logout-button" onClick={handleLogout}>
          {loading ? (
            <Spinner
              as="span"
              animation="border"
              size="sm"
              role="status"
              aria-hidden="true"
              className="me-2"
            />
          ) : null}
          <LogoutOutlined />
        </Button> */}
            <Dropdown
              className="logout-dropdown"
              menu={{ items: menuItems }}
              trigger={["click"]}
              onOpenChange={setVisible}
              open={visible}
            >
              <div className={`dropdown-trigger ${visible ? "active" : ""}`}>
                <Avatar className="user-avatar">
                  <UserOutlined />
                </Avatar>

                <span className="username"></span>

                <DownOutlined
                  className={`dropdown-icon ${visible ? "rotate" : ""}`}
                />
              </div>
            </Dropdown>
          </div>
        </div>
      </Header>
      <Layout>
        <Sider
          className="layout-sider"
          collapsible
          collapsed={collapsed}
          onCollapse={setCollapsed}
          style={{
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
          }}
          breakpoint="lg"
          collapsedWidth={60}
        >
          <Menu
            className="layout-menu"
            mode="inline"
            selectedKeys={[selectedKey]}
            openKeys={openKeys}
            onOpenChange={handleOpenChange}
            items={items2}
            onClick={handleMenuClick}
          />
        </Sider>
        <Content
          className={`layout-content ${
            collapsed ? "collapsed-content" : "expanded-content"
          }`}
        >
          <Outlet key={location.pathname} />
        </Content>
      </Layout>
    </Layout>
  );
};

export default LayoutDesign;
