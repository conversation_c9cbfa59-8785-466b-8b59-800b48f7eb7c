import { useEffect, useMemo, useState } from "react";
import "../../Products/Products.css";
import { message, Modal } from "antd";
import {
  Link,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import { axiosInstance } from "../../../apiCalls";
import DataTable from "../../UI/DataTable/DataTable";
import CommonPagination from "../../UI/Pagination/commonPagination";
import { useTableFilters } from "../../../customHooks/useFilter";
import SearchBox from "../../UI/SearchBox";
import Btn from "../../UI/Btn";

// const { Link } = Typography;

interface AttachedStoreCategory {
  id: number;
  store_name: string;
  store_id: number;
  store_code: string;
}

const AttachedStoreCategory: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [categories, setCategories] = useState<AttachedStoreCategory[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  // const [currentPage, setCurrentPage] = useState<number>(1);
  const [search, setSearch] = useState("");
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [searchParams] = useSearchParams();
  const tab = searchParams.get("tab") || "AttachedStore";
  const categoryName = searchParams.get("name") || "Category Name";

  const {
    currentPage,
    pageSize,
    filters,
    handleFilterChange,
    clearFilter,
    handlePageChange,
  } = useTableFilters();

  const [totalCount, setTotalCount] = useState<number>(0);

  const memoizedFilters = useMemo(() => filters, [filters]);

  const getCategories = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/v2/fetch-category-stores/${id}/`,
        {
          params: {
            ...memoizedFilters,
            page: currentPage,
            page_size: pageSize,
          },
        }
      );

      if (response.status === 201) {
        setCategories(response.data.objects);
        setTotalCount(response.data.total_count);
      } else {
        // console.error("Error fetching categories", response.status);
        setCategories([]);
      }
    } catch (error) {
      // console.error("Error fetching categories", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getCategories();
  }, [memoizedFilters, currentPage, pageSize]);

  const showDeleteConfirm = (categoryId: number) => {
    setDeleteId(categoryId);
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    if (!deleteId) return;
    try {
      await axiosInstance.delete(
        `api/menu/v2/delete-category-store/${deleteId}`
      );
      message.success("Category deleted successfully!");
      setCategories((prevCategories) =>
        prevCategories.filter((category) => category.id !== deleteId)
      );
    } catch (error) {
      message.error("Failed to delete category.");
      console.error("Error deleting category:", error);
    } finally {
      setIsDeleteModalOpen(false);
      setDeleteId(null);
    }
  };

  const handleFilter = () => {
    handleFilterChange("search", search);
  };
  const handleClear = () => {
    clearFilter("search");
    handleFilterChange("search", "");
    setSearch("");
  };

  const columns = [
    {
      title: "Store Name",
      dataIndex: "store_name",
      key: "store_name",
      width: "25%",
      fixed: "left" as "left",
      //   render: (text: string, record: AttachedStoreCategory) => (
      //     <span onClick={() => navigate(`./${record.id}`)}>
      //       <Link>{text}</Link>
      //     </span>
      //   ),
      // },
      render: (text: string, record: AttachedStoreCategory) => (
        <Link
          className="common-link text-decoration-none"
          to={`/stores/${record.store_id}`}
        >
          {text}
        </Link>
      ),
    },
    {
      title: "Store Code",
      dataIndex: "store_code",
      key: "store_code",
      width: "40%",
    },
    {
      title: "Actions",
      key: "actions",
      width: "15%",
      render: (_: any, record: AttachedStoreCategory) => (
        <Btn type="primary" danger onClick={() => showDeleteConfirm(record.id)}>
          Delete
        </Btn>
      ),
    },
  ];

  return (
    <div>
      <div className="container product-card-banner........................... d-flex flex-wrap">
        <div className="header products-headers d-flex flex-wrap">
          <div className="title">Attached Stores</div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            <div className="d-flex flex-wrap align-items-center">
              <div className="search-btn-driver">
                <SearchBox
                  value={search}
                  onChange={setSearch}
                  onSearch={handleFilter}
                  onClear={handleClear}
                  placeholder="Enter Name or Code"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex justify-end">
        <Btn
          className="btn-save hover:text-white"
          onClick={() => {
            navigate(
              `/categories/${id}/setstores?tab=${tab}&name=${categoryName}`
            );
          }}
        >
          Set Stores
        </Btn>
      </div>

      <div>
        <DataTable
          columns={columns}
          dataSource={categories}
          loading={loading}
          pagination={false}
          // scroll={{ x: 800, y: 500 }}
        />
      </div>
      <div className="d-flex justify-content-end mt-3">
        <CommonPagination
          current={currentPage}
          pageSize={pageSize}
          total={totalCount}
          onChange={handlePageChange}
          showSizeChanger
        />
      </div>
      <Modal
        title="Confirm Deletion"
        open={isDeleteModalOpen}
        onOk={handleDelete}
        onCancel={() => setIsDeleteModalOpen(false)}
        okText="Delete"
        okType="danger"
        cancelText="Cancel"
      >
        <p>Are you sure you want to delete this store?</p>
      </Modal>
    </div>
  );
};

export default AttachedStoreCategory;
