import React, { useEffect, useState } from "react";
import { Card, Button, Upload, notification } from "antd";
import { UploadOutlined } from "@ant-design/icons";
import { useParams, useSearchParams } from "react-router-dom";
import { axiosInstance } from "../../apiCalls";
import type { RcFile } from "antd/es/upload";
import axios from "axios";
import ProductVariants from "./Variants/ProductVariants";
import { useProduct } from "../../context/ProductContext";
import BackButton from "../UI/BackButton";

const tabList = [
  { key: "Details", tab: "Details" },
  { key: "Variants", tab: "Variants" },
];
export interface productDetailsData {
  id: number;
  code: String;
  sku: String;
  name: String;
  display_name: String;
  description: String;
  markdown_description: String;
  short_description: String;
  position: number;
  product_type: String;
  image_large_url?: String;
  image_thumbnail_url?: String;
}

const ProductDetails: React.FC = () => {
  // const { id } = useParams();
  const { code } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();

  const { productId, setProductId } = useProduct();
  const activeTabKey1 = searchParams.get("tab") || "Details";
  const [productDetails, setProductDetails] =
    useState<productDetailsData | null>(null);
  const [uploading, setUploading] = useState<boolean>(false);
  // const [key, setKey] = useState("");
  // const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
  //   {}
  // );
  // const [uploadSuccess, setUploadSuccess] = useState<Record<string, boolean>>(
  //   {}
  // );
  useEffect(() => {
  const fetchProduct = async () => {
    try {
      const response = await axiosInstance.get(`/api/menu/products/${code}/`);
      if (response.status === 200) {
        setProductDetails(response.data);

        // Only update productId if it has not been set or is different
        if (productId !== response.data.id) {
          setProductId(response.data.id);
        }
      } else {
        setProductDetails(null);
      }
    } catch (error) {
      console.error("Error fetching menu data", error);
    }
  };

  fetchProduct();
// eslint-disable-next-line react-hooks/exhaustive-deps
}, [code]);  // only fetch when code changes


  // useEffect(() => {
  //   fetchProduct();
  // }, [code]);
  // useEffect(() => {
  //   console.log(productDetails);
  // }, [productDetails]);
  // console.log("productDetails:", productDetails);

  const onTabChange = (key: string) => {
    setSearchParams({ tab: key }); // Update the URL query param
  };
  const getPresignedUrl = async (file: RcFile) => {
    try {
      // console.log("Fetching presigned URL for:", file.name);
      const response = await axiosInstance.post(
        "/api/utilities/get-file-upload-url/",
        {
          file_name: file.name,
          file_type: "thumbnail",
        }
      );

      if (
        !response.data.url ||
        !response.data.url.url ||
        !response.data.url.fields
      ) {
        return null;
      }

      const fileKey = response.data?.url?.fields?.key;
      // console.log("fileKey:",fileKey);
      // console.log("typeOf",typeof(fileKey));

      if (!fileKey) {
        return null;
      }

      return {
        url: response.data.url.url, // Extracting correct URL
        fields: response.data.url.fields, // Extracting correct fields
        key: response.data?.url?.fields?.key,
      };
    } catch (error) {
      notification.error({
        message: "Error",
        description: `Failed to get presigned URL for ${file.name}.`,
      });
      return null;
    }
  };

  const uploadToS3 = async (
    uploadData: any,
    file: RcFile,
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => {
    setUploading(true);
    try {
      if (!uploadData?.url || !uploadData?.fields) {
        // console.error("Invalid upload data:", uploadData);
        return;
      }

      const formData = new FormData();
      Object.entries(uploadData.fields).forEach(([key, value]) => {
        formData.append(key, value as string);
      });
      formData.append("file", file);

      const response = await axios.post(uploadData.url, formData, {
        headers: { "Content-Type": "multipart/form-data" },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.loaded && progressEvent.total) {
            // const percent = Math.round(
            //   (progressEvent.loaded / progressEvent.total) * 100
            // );
            // setUploadProgress((prev) => ({ ...prev, [file.name]: percent }));
          }
        },
      });

      // setUploadSuccess((prev) => ({ ...prev, [file.name]: true }));
      // console.log("key:", key);
      notification.success({
        message: "Upload Successful",
        description: `${file.name} uploaded successfully.`,
      });
      console.log("uploads3:", response);

      await updateProductImage(uploadData.key, fieldType);
    } catch (error) {
      // setUploadSuccess((prev) => ({ ...prev, [file.name]: false }));

      notification.error({
        message: "Upload Failed",
        description: `Failed to upload ${file.name}.`,
      });
    } finally {
      setUploading(false);
    }
  };

  const handleUpload = async (
    file: RcFile,
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => {
    // console.log("handleUpload called for:", file.name, "Type:", fieldType);
    const uploadData = await getPresignedUrl(file);
    if (!uploadData) {
      // console.error("uploadData is null, stopping upload.");
      return;
    }
    await uploadToS3(uploadData, file, fieldType);
  };

  const updateProductImage = async (
    imageUrl: string,
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => {
    try {
      // console.log("Updating product image:", fieldType, imageUrl);
      await axiosInstance.patch(`/api/menu/products/${productDetails?.code}/`, {
        [fieldType]: imageUrl,
      });
     // fetchProduct();
    } catch (error) {
      notification.error({
        message: "Update Failed",
        description: "Failed to update product image.",
      });
    }
  };

  const getUploadProps = (
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => ({
    beforeUpload: (file: RcFile) => {
      handleUpload(file, fieldType);
      return false; // Prevent default upload behavior
    },
    showUploadList: false, // Hide default file list UI
  });

  const contentList: Record<string, React.ReactNode> = {
    Details: productDetails ? (
      <div>
        <p>
          <strong>Code:</strong> {productDetails.code}
        </p>
        <p>
          <strong>SKU:</strong> {productDetails.sku || "N/A"}
        </p>
        <p>
          <strong>Name:</strong> {productDetails.name}
        </p>
        <p>
          <strong>Display Name:</strong> {productDetails.display_name}
        </p>
        <p>
          <strong>Description:</strong> {productDetails.description}
        </p>
        <p>
          <strong>Markdown Description:</strong>{" "}
          {productDetails.markdown_description}
        </p>
        <p>
          <strong>Short Description:</strong> {productDetails.short_description}
        </p>
        {/* <p>
          <strong>Position:</strong> {productDetails.position}
        </p> */}
        <p>
          <strong>Product Type:</strong> {productDetails.product_type}
        </p>
        <div className="mb-2">
          <strong>Large Image URL:</strong>{" "}
          {productDetails.image_large_url ? (
            <img
              src={String(productDetails.image_large_url)}
              alt="Large"
              width={100}
            />
          ) : (
            <Upload {...getUploadProps("image_large_url")}>
              <Button icon={<UploadOutlined />} loading={uploading}>
                Upload Large Image
              </Button>
            </Upload>
          )}
        </div>

        <div>
          <strong>Thumbnail URL:</strong>{" "}
          {productDetails.image_thumbnail_url ? (
            <img
              src={String(productDetails.image_thumbnail_url)}
              alt="Thumbnail"
              width={50}
            />
          ) : (
            <Upload {...getUploadProps("image_thumbnail_url")}>
              <Button icon={<UploadOutlined />} loading={uploading}>
                Upload Thumbnail
              </Button>
            </Upload>
          )}
        </div>
      </div>
    ) : (
      <p>Loading...</p>
    ),
    Variants: <ProductVariants />,
  };

  return (
    <>
      <div>
        <BackButton to={`/products`} />
      </div>

      <Card
        style={{ width: "100%" }}
        title="Product Details"
        tabList={tabList}
        activeTabKey={activeTabKey1}
        onTabChange={onTabChange}
      >
        {contentList[activeTabKey1]}
      </Card>
      <br />
      <br />
    </>
  );
};

export default ProductDetails;
