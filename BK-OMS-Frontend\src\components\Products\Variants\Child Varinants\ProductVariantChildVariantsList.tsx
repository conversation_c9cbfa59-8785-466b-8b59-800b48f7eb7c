import { ProductVariantChildVariantProps } from "../../../../types/Products";
import { message } from "antd";
import { useParams } from "react-router-dom";
import StoreTabs from "../../../../reusableComponents/store/StoreTabs";
import { useState } from "react";
import { axiosInstance } from "../../../../apiCalls";
import StatusToggle from "../../../UI/ToggleStatus";
import showConfirmActionModal from "../../../UI/PopUpModal";
import axios from "axios";

//const { Link } = Typography;

const ProductVariantChildVariantsList = () => {
  // const navigate = useNavigate();
  const { variantid } = useParams() as { variantid: string };

  // const [loading, setLoading] = useState(false);

  const [refreshTrigger, setRefreshTrigger] = useState<number>(0);

  // State for delete confirmation
  // const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  // const [deleteIds, setDeleteIds] = useState<{
  //   parentId: number;
  //   childId: number;
  // } | null>(null);

  // Function to open the delete confirmation modal
  // const showDeleteConfirm = (parentId: number, childId: number) => {
  //   setDeleteIds({ parentId, childId });
  //   setIsDeleteModalOpen(true);
  // };

  // Function to handle deletion
  // const handleDelete = async () => {
  //   if (!deleteIds) return;

  //   try {
  //     const response = await axiosInstance.delete(
  //       `api/menu/v2/child-variants/${deleteIds.parentId}/${deleteIds.childId}/`
  //     );
  //     if (response.status == 204) {
  //       message.success("successfully deleted child variants");
  //       window.location.reload();
  //     } else {
  //       message.error("Failed to delete");
  //     }
  //   } catch (error) {
  //     message.error("Failed to delete");
  //   } finally {
  //     setIsDeleteModalOpen(false);
  //     setDeleteIds(null);
  //   }
  // };

  const handleStatusChange = (id: number, isActive: boolean) => {
    // console.log("Creative ID:", creative_id);
    // console.log("Is Active:", isActive);
    const confirmUpdate = async () => {
      try {
        // setLoading(true);
        const response = await axiosInstance.put(
          `/cms/menu/creatives-details/${id}/`,
          {
            is_active: isActive,
          }
        );

        if (response.status === 200) {
          message.success(`Child VariantStatus ${response.data.message}`);
          setRefreshTrigger((p) => p + 1);
        } else {
          message.error("Failed to update user status.");
        }
      } catch (err: unknown) {
        if (axios.isAxiosError(err)) {
          message.error(
            err.response?.data?.message ||
              "An error occurred while updating user status."
          );
        } else {
          message.error("An unexpected error occurred.");
        }
      } finally {
        // setLoading(false);
      }
    };

    showConfirmActionModal({
      isActive,
      onConfirm: confirmUpdate,
      entityName: "Product Child Variant",
    });
  };

  const columns = [
    {
      title: "POS Number",
      dataIndex: "child_pos",
      key: "child_pos",
      width: "10%",
      fixed: "left" as "left",
      // render: (text: string, _: ProductVariantChildVariantProps) => (
      //   <span onClick={() => console.log("Clicked ID")}>
      //     <Link>{text}</Link>
      //   </span>
      // ),
    },
    {
      title: "Child Code",
      dataIndex: "child_code",
      key: "child_code",
      width: "15%",
    },

    {
      title: "Child",
      dataIndex: "child",
      key: "child",
      width: "10%",
    },

    {
      title: "Name",
      dataIndex: "child_name",
      key: "child_name",
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
    },
    {
      title: "Section",
      dataIndex: "section",
      key: "section",
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      render: (is_active: boolean, record: ProductVariantChildVariantProps) => (
        <>
          <StatusToggle
            isActive={is_active}
            id={record.id}
            onToggle={() => handleStatusChange(record.id, !record.is_active)}
          />
        </>
      ),
    },
    // {
    //   title: "Delete",
    //   key: "delete",
    //   render: (_: any, record: ProductVariantChildVariantProps) => (
    //     <Button
    //       type="primary"
    //       danger
    //       onClick={() => showDeleteConfirm(record.parent, record.id)}
    //     >
    //       Delete
    //     </Button>
    //   ),
    // },
  ];

  const mapData = (response: any): ProductVariantChildVariantProps[] => {
    if (!Array.isArray(response)) {
      console.error("Invalid API response format:", response);
      return [];
    }

    return response.map((item: any) => ({
      key: item.id,
      id: item.id,
      child_code: item.child_code,
      child_pos: item.child_pos,
      section: item.section,
      parent: item.parent,
      child: item.child,
      position: item.position,
      child_name: item.child_name,
      is_active: item.is_active,
    }));
  };

  return (
    <>
      <StoreTabs
        id={variantid}
        apiEndpoint="api/menu/v2/child-variants"
        name="Child Variants"
        columns={columns}
        dataMapper={mapData}
        add={`/products/variants/${variantid}/childvariant/add`}
        refreshTrigger={refreshTrigger}
      />

      {/* Delete Confirmation Modal */}
      {/* <Modal
        title="Confirm Deletion"
        open={isDeleteModalOpen}
        onOk={handleDelete}
        onCancel={() => setIsDeleteModalOpen(false)}
        okText="Delete"
        okType="danger"
        cancelText="Cancel"
      >
        <p>Are you sure you want to delete this child variant?</p>
      </Modal> */}
    </>
  );
};

export default ProductVariantChildVariantsList;
