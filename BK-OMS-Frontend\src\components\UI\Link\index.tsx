import { Link, To } from "react-router-dom";
import { memo, ReactNode } from "react";

interface LinkProps {
  to: To;
  children: ReactNode;
  className?: string;
  ariaLabel?: string;
}

const LINK = ({ to, children, className = "", ariaLabel }: LinkProps) => {
  return (
    <Link
      to={to}
      className={`common-link text-decoration-none ${className}`}
      aria-label={ariaLabel}
    >
      {children}
    </Link>
  );
};

export default memo(LINK);
