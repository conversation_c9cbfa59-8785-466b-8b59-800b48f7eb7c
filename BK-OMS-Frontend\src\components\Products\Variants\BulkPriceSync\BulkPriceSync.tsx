
import { useState } from 'react';
import { Button, Modal, Form, InputNumber, Upload, message, notification } from 'antd';
import { UploadOutlined, } from '@ant-design/icons';
import { axiosInstance } from '../../../../apiCalls';


const PriceEditModal = ({ visible, onClose, productId, onSuccess }) => {
  const [form] = Form.useForm();
  const [uploadVisible, setUploadVisible] = useState(false);
  const [storeCodes, setStoreCodes] = useState<string[]>([]);
  const [csvFile, setCsvFile] = useState(null);

  const handleUpdatePrice = async () => {
    try {
      const values = await form.validateFields();
      if (!storeCodes.length) {
        message.warning('Please upload a CSV file with store codes');
        return;
      }
      const payload = {
        product_id: productId,
        product_price: values.price,
        product_discount_price: values.discount,
        store_codes: storeCodes,
      };

      const response = await axiosInstance.post('api/menu/bulk-price-update-storevariant/', payload);

      if (response.status === 200) {
        notification.success({ message: response.data.message });
        onSuccess();
        onClose();
        form.resetFields();
        setStoreCodes([]);
        setCsvFile(null);
      }
    } catch (error) {
      notification.error({ message: 'Update Failed', description: 'Could not update prices.' });
    }
  };

  const handleCSVUpload = (file) => {
   
  };

  return (
    <Modal
      title="Edit Price and Discount"
      open={visible}
      onCancel={onClose}
      onOk={handleUpdatePrice}
      okText="Update"
    >
      <Form layout="vertical" form={form}>
        <Form.Item
          label="Price"
          name="price"
          rules={[{ required: true, message: 'Please enter price' }]}
        >
          <InputNumber className="w-full" min={0} />
        </Form.Item>

        <Form.Item
          label="Discounted Price"
          name="discount"
          rules={[{ required: true, message: 'Please enter discount price' }]}
        >
          <InputNumber className="w-full" min={0} />
        </Form.Item>

        <Form.Item label="Upload Store CSV">
          <Upload
            beforeUpload={handleCSVUpload}
            fileList={csvFile ? [csvFile] : []}
            onRemove={() => setCsvFile(null)}
          >
            <Button icon={<UploadOutlined />}>Upload CSV (store_code)</Button>
          </Upload>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PriceEditModal;
