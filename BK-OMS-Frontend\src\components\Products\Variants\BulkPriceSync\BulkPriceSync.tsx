import React, { useState } from "react";
import {
  Button,
  Modal,
  Form,
  InputNumber,
  Upload,
  message,
  notification,
  Tooltip,
} from "antd";
import { DownloadOutlined, UploadOutlined } from "@ant-design/icons";
import { axiosInstance } from "../../../../apiCalls";
import type { UploadFile } from "antd/es/upload/interface";
import Btn from "../../../UI/Btn";
import { SAMPLE_CSV } from "../../../Menu/MenuPush/Text/Contants";

// Define props interface
interface PriceEditModalProps {
  visible: boolean;
  onClose: () => void;
  productId: number;
  onSuccess: () => void;
}

const PriceEditModal: React.FC<PriceEditModalProps> = ({
  visible,
  onClose,
  productId,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [storeCodes, setStoreCodes] = useState<string[]>([]);
  const [csvFile, setCsvFile] = useState<UploadFile | null>(null);
  const [csvUploading, setCsvUploading] = useState(false);

  const [isDownloaded, setIsDownloaded] = useState(false);

  const handleUpdatePrice = async () => {
    try {
      const values = await form.validateFields();
      console.log("Form values:", values);
      console.log("Current store codes:", storeCodes);

      if (!storeCodes.length) {
        message.warning("Please upload a CSV file with store codes");
        return;
      }

      const payload = {
        product_id: productId,
        product_price: values.price,
        product_discount_price: values.discount,
        store_codes: storeCodes,
      };

      console.log("Sending payload:", payload);

      const response = await axiosInstance.post(
        "api/menu/bulk-price-update-storevariant/",
        payload
      );

      console.log("API response:", response);

      if (response.status === 200) {
        notification.success({ message: response.data.message });
        onSuccess();
        onClose();
        form.resetFields();
        setStoreCodes([]);
        setCsvFile(null);
      }
    } catch (error) {
      console.error("API error:", error);
      notification.error({
        message: "Update Failed",
        description: "Could not update prices.",
      });
    }
  };

  // CSV parsing function
  const parseCSV = (csvText: string): string[] => {
    console.log("Raw CSV text:", csvText);

    // Handle different line endings (Windows \r\n, Unix \n, Mac \r)
    const lines = csvText.split(/\r?\n|\r/).filter((line) => line.trim());
    console.log("Filtered lines:", lines);

    if (lines.length === 0) return [];

    // Function to parse a CSV line properly handling quoted fields
    const parseCSVLine = (line: string): string[] => {
      const result: string[] = [];
      let current = "";
      let inQuotes = false;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === "," && !inQuotes) {
          result.push(current.trim());
          current = "";
        } else {
          current += char;
        }
      }

      // Add the last field
      result.push(current.trim());
      return result.map((field) => field.replace(/^"|"$/g, ""));
    };

    // Get headers (first line)
    const headers = parseCSVLine(lines[0]).map((h) => h.trim().toLowerCase());
    console.log("Headers found:", headers);

    // Look for store code column with more flexible matching
    const storeCodeIndex = headers.findIndex(
      (h) => h.includes("store") && h.includes("code") ||
             h === "store_code" ||
             h === "storecode" ||
             h === "code"
    );

    console.log("Store code index:", storeCodeIndex);

    if (storeCodeIndex === -1) {
      throw new Error('CSV must contain a "Store Code" column (or similar: store_code, storecode, code)');
    }

    // Parse data rows and extract store codes
    const codes: string[] = [];
    for (let i = 1; i < lines.length; i++) {
      const values = parseCSVLine(lines[i]);
      console.log(`Row ${i} values:`, values);

      if (values.length > storeCodeIndex) {
        const storeCode = values[storeCodeIndex]?.trim() || "";
        console.log(`Row ${i} store code:`, storeCode);

        if (storeCode && storeCode !== "") {
          codes.push(storeCode);
        }
      }
    }

    console.log("Final extracted codes:", codes);
    return codes;
  };

  const handleCSVUpload = (file: File) => {
    console.log("Starting CSV upload for file:", file.name);
    setCsvUploading(true);
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const csvText = e.target?.result as string;
        console.log("File read successfully, parsing CSV...");

        const parsedCodes = parseCSV(csvText);

        if (parsedCodes.length === 0) {
          message.warning("No valid store codes found in CSV file");
          setCsvUploading(false);
          return;
        }

        console.log("Setting store codes:", parsedCodes);
        setStoreCodes(parsedCodes);
        setCsvFile({
          uid: file.name,
          name: file.name,
          status: "done",
          size: file.size,
          type: file.type,
        });

        message.success(
          `Successfully loaded ${parsedCodes.length} store codes from CSV: ${parsedCodes.join(", ")}`
        );
      } catch (error) {
        console.error("Error parsing CSV:", error);
        message.error(
          error instanceof Error ? error.message : "Failed to parse CSV file"
        );
      } finally {
        setCsvUploading(false);
      }
    };

    reader.onerror = () => {
      console.error("FileReader error");
      message.error("Failed to read CSV file");
      setCsvUploading(false);
    };

    reader.readAsText(file);
    return false; // Prevent default upload behavior
  };

  const handleRemoveFile = () => {
    setCsvFile(null);
    setStoreCodes([]);
    message.info("CSV file removed");
  };

  const handleCancel = () => {
    form.resetFields();
    setStoreCodes([]);
    setCsvFile(null);
    onClose();
  };

  const downloadSampleCSV = () => {
    try {
      setIsDownloaded(true);
      console.log("Downloading sample CSV...");
    } catch (error) {
    } finally {
      setIsDownloaded(false);
    }
  };

  return (
    <Modal
      title={
        <div
          // style={{
          //   display: "flex",
          //   justifyContent: "space-between",
          //   alignItems: "center",
          //   borderBottom: "2px solid #fa8431",
          //   paddingBottom: "10px",
          //   width: "100%",
          // }}
          className="modal-title"
        >
          <span>Bulk Price Sync</span>
          <div
            // style={{
            //   display: "flex",
            //   justifyContent: "center",
            //   marginRight: "20px",
            //   flexGrow: 1,
            // }}
            className="d-flex justify-content-center align-items-center flex-grow-1"
          >
            <Tooltip title="Download sample CSV format">
              <Button
                icon={<DownloadOutlined />}
                onClick={downloadSampleCSV}
                size="small"
                shape="round"
                type="link"
                className="btn-download-sample-csv"
              >
                {isDownloaded ? "Downloading..." : `${SAMPLE_CSV}`}
              </Button>
            </Tooltip>
          </div>
        </div>
      }
      maskClosable={false}
      open={visible}
      onCancel={handleCancel}
      onOk={handleUpdatePrice}
      okText="Sync Price to Stores"
      cancelText="Cancel"
      okButtonProps={{
        className: "px-4 custom-modal-ok-button",
      }}
      cancelButtonProps={{
        className: "y px-4 custom-modal-cancel-button",
      }}
      confirmLoading={csvUploading}
    >
      <Form
        form={form}
        layout="vertical"
        className="add-store-form border-1 border-gray-200 rounded-md p-4"
      >
        <Form.Item
          label="Price"
          name="price"
          rules={[{ required: true, message: "Please enter price" }]}
        >
          <InputNumber className="w-full" min={0} step={0.01} />
        </Form.Item>

        <Form.Item
          label="Discounted Price"
          name="discount"
          rules={[{ required: true, message: "Please enter discount price" }]}
        >
          <InputNumber className="w-full" min={0} step={0.01} />
        </Form.Item>

        <Form.Item
          label="Upload Store CSV"
          help="CSV file should contain a 'Store Code' column with store codes"
        >
          <Upload
            accept=".csv"
            beforeUpload={handleCSVUpload}
            fileList={csvFile ? [csvFile] : []}
            onRemove={handleRemoveFile}
            showUploadList={{
              showPreviewIcon: false,
              showDownloadIcon: false,
            }}
            disabled={csvUploading}
          >
            <Btn
              icon={<UploadOutlined />}
              loading={csvUploading}
              disabled={csvUploading}
            >
              {csvUploading ? "Processing..." : "Upload CSV (store_code)"}
            </Btn>
          </Upload>
          {storeCodes.length > 0 && (
            <div style={{ marginTop: 8 }}>
              <div style={{ color: "#52c41a", marginBottom: 4 }}>
                ✓ {storeCodes.length} store codes loaded
              </div>
              <div style={{
                fontSize: "12px",
                color: "#666",
                backgroundColor: "#f5f5f5",
                padding: "8px",
                borderRadius: "4px",
                maxHeight: "100px",
                overflowY: "auto"
              }}>
                <strong>Store Codes:</strong> {storeCodes.join(", ")}
              </div>
            </div>
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PriceEditModal;
