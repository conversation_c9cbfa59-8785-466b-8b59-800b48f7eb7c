// src/components/StoreCategory/StoreCatagory.tsx
import React, { useState, useEffect, useMemo } from "react";
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button, message, Modal } from "antd";
import { CheckOutlined, CloseOutlined, EditOutlined } from "@ant-design/icons";
import { axiosInstance } from "../../../apiCalls";
import type { StoreCatagoryProps } from "../../../types/index";
import { useTableFilters } from "../../../customHooks/useFilter";
import SearchBox from "../../UI/SearchBox";
import DataTable from "../../UI/DataTable/DataTable";
import Link from "../../UI/Link";

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  "data-row-key": string;
}

const DragRow: React.FC<RowProps & { dragEnabled: boolean }> = ({
  dragEnabled,
  ...props
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props["data-row-key"] });
  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    cursor: "move",
    ...(isDragging ? { position: "relative", zIndex: 9999 } : {}),
  };

  return (
    <tr
      {...props}
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
    />
  );
};

interface StoreCategoryProps {
  id: string;
}
interface CategoryWithKey extends StoreCatagoryProps {
  key: string;
}

const StoreCatagory: React.FC<StoreCategoryProps> = ({ id }) => {
  const {
    currentPage,
    pageSize,
    setPageSize,
    // handlePageChange,
    handleFilterChange,
    filters,
  } = useTableFilters();
  const [categories, setCategories] = useState<CategoryWithKey[]>([]);
  const [dragEnabled, setDragEnabled] = useState(false);
  const [loading, setLoading] = useState(false);
  // const [editingCategoryId, setEditingCategoryId] = useState<number | null>(
  //   null
  // );
  // const [totalCount, setTotalCount] = useState<number>(0);
  // const [editingPosition, setEditingPosition] = useState<number | null>(null);
  // // const [refreshTrigger, setRefreshTrigger] = useState(0);

  const [search, setSearch] = useState("");

  // const inputRef = useRef<HTMLInputElement>(null);
  // const navigate = useNavigate();

  const memoizedFilters = useMemo(() => filters, [filters]);

  const fetchCategories = async () => {
    setLoading(true);
    try {
      const { data, status } = await axiosInstance.get(
        `/api/menu/store-categories/?store_id=${id}`,
        {
          params: {
            page: currentPage,
            page_size: pageSize,
            ...memoizedFilters,
          },
        }
      );
      if (status === 200) {
        // setCategories(
        //   data.objects.map((item: any) => ({
        //     ...item,
        //     key: item.id.toString(),
        //   }))
        // );
        const sorted = data
          .map((item: any) => ({
            ...item,
            key: item.id.toString(),
          }))
          .sort((a: any, b: any) => a.position - b.position);
        setCategories(sorted);
        //setTotalCount(data.total_count);
      }
    } catch (err) {
      console.error(err);
      message.error("Failed to load categories.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, [id, currentPage, pageSize, memoizedFilters]);

  useEffect(() => {
    if (filters.search) {
      setSearch(filters.search);
    } else {
      setSearch("");
    }
  }, [filters]);

  const patchCategory = async (
    category_id: number,
    updates: Partial<{ position: number; is_active: boolean }>
  ) => {
    return axiosInstance.patch(`/api/menu/store-categories/${category_id}/`, {
      store_id: Number(id),
      category_id,
      ...updates,
    });
  };

  const handleStatusChange = (
    category: StoreCatagoryProps,
    newState: boolean
  ) => {
    Modal.confirm({
      title: newState ? "Activate Category" : "Deactivate Category",
      content: `Are you sure you want to ${
        newState ? "activate" : "deactivate"
      } this category?`,
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: async () => {
        setLoading(true);
        try {
          const { status } = await patchCategory(category.id, {
            is_active: newState,
          });
          console.log(status);
          if (status === 200) {
            message.success("Status updated Successfully.");
            await fetchCategories();
            // setRefreshTrigger((p) => p + 1);
          } else {
            throw new Error();
          }
        } catch {
          message.error("Failed to update status.");
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // const handleSubmitPosition = async (category: StoreCatagoryProps) => {
  //   if (editingPosition == null) return;
  //   setLoading(true);
  //   try {
  //     const { status } = await patchCategory(category.id, {
  //       position: editingPosition,
  //     });
  //     if (status === 200) {
  //       message.success("Position updated.");
  //       setEditingCategoryId(null);
  //       setEditingPosition(null);
  //       fetchCategories();
  //       //setRefreshTrigger((p) => p + 1);
  //     } else {
  //       throw new Error();
  //     }
  //   } catch {
  //     message.error("Failed to update position.");
  //   } finally {
  //     setLoading(false);
  //   }
  // };
  useEffect(() => {
    if (dragEnabled) {
      setPageSize(100);
    } else {
      setPageSize(10);
    }
  }, [dragEnabled]);

  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 1 } })
  );
  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (!dragEnabled || !over || active.id === over.id) return;

    const oldIndex = categories.findIndex((c) => c.key === active.id);
    const newIndex = categories.findIndex((c) => c.key === over.id);
    const newOrder = arrayMove(categories, oldIndex, newIndex);
    setCategories(newOrder);
  };

  // const handleSearchChange = (value: string) => {
  //   handleFilterChange("search", value);
  // };

  const handleClear = () => {
    setSearch("");
    handleFilterChange("search", "");
  };
  const handleSaveNewOrder = async () => {
    try {
      const updatedCategories = categories.map((item, idx) => ({
        ...item,
        position: idx + 1,
      }));

      const response = await axiosInstance.patch(
        `api/menu/store-categories-position-update/`,
        {
          list: updatedCategories,
        }
      );
      if (response.status === 200) {
        message.success("Categories reordered successfully.");
        fetchCategories();
      }
    } catch {
      message.error("Failed to save new order.");
    }
  };

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      // fixed: "left",
      width: "20%",
      render: (text: string, record: StoreCatagoryProps) => (
        // <Link
        //   onClick={() =>
        //     navigate(
        //       `/stores/${id}/category?storeid=${id}&id=${record.id}&name=${record.name}`
        //     )
        //   }
        // >
        //   {record.name}
        // </Link>
        <>
          {text ? (
            <Link
              className="common-link text-decoration-none"
              to={`/stores/${id}/category?storeid=${id}&id=${record.id}&name=${record.name}`}
            >
              {text}
            </Link>
          ) : (
            "-"
          )}
        </>
      ),
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
      width: "20%",
      render: (_: any, record: StoreCatagoryProps) => (
        <>
          {record.position}
          <Button
            type="link"
            onClick={() => {
              setDragEnabled(true);
              message.info("Drag and drop is now enabled.");
            }}
          >
            <EditOutlined className="btn-edit-pencil" />
          </Button>
          {dragEnabled && (
            <Button
              icon={<CheckOutlined />}
              onClick={() => {
                setDragEnabled(false);
                handleSaveNewOrder();
              }}
            />
          )}
        </>
      ),
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      width: "15%",
      render: (isActive: boolean, record: StoreCatagoryProps) => (
        <div
          className={`switch-button ${isActive ? "checked" : ""}`}
          onClick={() => handleStatusChange(record, !isActive)}
        >
          <span className="switch-label">
            {isActive ? <CheckOutlined /> : <CloseOutlined />}
          </span>
          <div className="switch-handle" />
        </div>
      ),
    },
  ];
  useEffect(() => {
    if (dragEnabled) {
    }
  }, [dragEnabled]);

  return (
    <>
      <SearchBox
        value={search}
        onChange={(v) => setSearch(v)}
        onSearch={() => handleFilterChange("search", search)}
        onClear={handleClear}
        placeholder="Enter Name"
      />

      {dragEnabled ? (
        <DndContext
          sensors={sensors}
          modifiers={[restrictToVerticalAxis]}
          onDragEnd={onDragEnd}
        >
          <SortableContext
            items={categories.map((c) => c.key)}
            strategy={verticalListSortingStrategy}
          >
            <DataTable
              loading={loading}
              rowKey="key"
              columns={columns}
              dataSource={categories}
              components={{
                body: {
                  row: (props: RowProps) => (
                    <DragRow {...props} dragEnabled={dragEnabled} />
                  ),
                },
              }}
              pagination={false}
              scroll={{ x: "max-content" }}
            />
          </SortableContext>
        </DndContext>
      ) : (
        <DataTable
          loading={loading}
          rowKey="key"
          columns={columns}
          dataSource={categories}
          pagination={false}
          scroll={{ x: "max-content" }}
        />
      )}

      {/* <div className="d-flex justify-content-end mt-4">
        <CommonPagination
          current={currentPage}
          total={totalCount}
          pageSize={pageSize}
          showSizeChanger={true}
          onChange={handlePageChange}
          onShowSizeChange={handlePageChange}
        />
      </div> */}
    </>
  );
};

export default StoreCatagory;
