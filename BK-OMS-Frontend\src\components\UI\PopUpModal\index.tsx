// src/utils/showConfirmActionModal.ts
import { Modal } from "antd";

interface ShowConfirmActionModalProps {
  isActive: boolean;
  Text?: string;
  onConfirm: () => Promise<void> | void;
  entityName?: string;
}

const showConfirmActionModal = ({
  isActive,
  onConfirm,
  Text,
  entityName = "this item",
}: ShowConfirmActionModalProps): void => {
  const actionText = Text || (isActive ? "activate" : "deactivate");

  Modal.confirm({
    title: isActive
      ? `${actionText} ${entityName}`
      : `${actionText} ${entityName}`,
    content: `Are you sure you want to ${actionText} ${entityName}?`,
    okText: "Yes",
    cancelText: "No",
    className: "custom-modal",
    okButtonProps: { className: "custom-modal-ok-button" },
    cancelButtonProps: { className: "custom-modal-cancel-button" },
    onOk: onConfirm,
  });
};

export default showConfirmActionModal;
