import React, { memo } from "react";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  loading?: boolean;
  variant?: "primary" | "secondary" | "danger" | "default";
}

const Button: React.FC<ButtonProps> = ({
  children,
  loading = false,
  disabled = false,
  className = "",
  variant = "default",
  ...rest
}) => {
  return (
    <button className={`${className}`} disabled={disabled || loading} {...rest}>
      {loading ? "Loading..." : children}
    </button>
  );
};

export default memo(Button);
