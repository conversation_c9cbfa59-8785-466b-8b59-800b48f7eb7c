export interface ProductVariantProps {
  id: number;
  code: string;
  sku: string;
  name: string;
  display_name: string;
  description: string;
  product_type: string;
  base_product: number;
  default_pop_variant: number;
  image_large_url: string;
  image_thumbnail_url: string;
  is_crown_product: boolean;
  loyalty_offer_code: string;
  crown_points: number;
  base_product_name: string;
  default_pop_variant_name: string;
  unavailability_tags: string[];
  is_available: boolean;
  tags: string[];
}

export interface ProductVariantModiferGroupProps {
  id: number;
  position: number;
  is_active: boolean;
  product_variant: number;
  modifier_group: number;
}

export interface ProductVariantModiferProps {
  id: number;
  product: number;
  is_active: boolean;
}

export interface ProductVariantChildVariantProps {
  id: number;
  child_pos: number;
  child_code: string;
  section: string;
  parent: number;
  child: number;
  // position: number;
  // child_name: string;
   is_active: boolean;
}

export interface StoreProductVariantsProps {
  id: number;
  position: number;
  price: number;
  variant: number;
  store_name: string;
  category: number;
}
export interface ModifierGroupModifierProps {
  id: number;
  name: string;
  price: number;
  position: number;
  default_qty: number;
  max_qty: number;
  upsell_to_packaged_version: boolean;
  is_active: boolean;
  modifier_group: number;
  modifier: number;
}

export interface ModifierGroupDetailsPageType {
  id: number;
  code: string;
  name: string;
  display_name: string;
  description: string;
  section: string;
  position: number;
  is_active: boolean;
  use_inventory_price: boolean;
  max_selectable: number;
  category: string;
}
