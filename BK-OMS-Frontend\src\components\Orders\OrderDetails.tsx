import React, { useCallback, useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import dayjs from "dayjs";
import { CopyToClipboard } from "react-copy-to-clipboard";
import { DoubleRightOutlined } from "@ant-design/icons";
import { axiosInstance } from "../../apiCalls";
import "./Orders.css";
import { Item, OrderType, ProcessedProduct, ProductDetails } from "../../types";
import { Alert, message, Modal, Spin } from "antd";
import Btn from "../UI/Btn";
import showConfirmActionModal from "../UI/PopUpModal";

const copyButtonStyle = (copied: boolean) => ({
  backgroundColor: copied ? "#52c41a" : "#fa8431",
  borderColor: copied ? "#52c41a" : "#fa8431",
  transition: "background-color 0.3s ease, border-color 0.3s ease",
  padding: "6px 20px",
  fontSize: "14px",
  fontWeight: "600",
  boxShadow: copied
    ? "0 4px 8px rgba(82, 196, 26, 0.3)"
    : "0 4px 8px rgba(24, 144, 255, 0.3)",
});

const OrderDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [order, setOrder] = useState<OrderType | undefined>(undefined);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [productDetails, setProductDetails] = useState<any[]>([]);

  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [jsonData, setJsonData] = useState<string>("");
  const [copied, setCopied] = useState<boolean>(false);
  const [modalTitle, setModalTitle] = useState<string>("");

  const [isNCRSyncedLoading, setIsNCRSyncedLoading] = useState<boolean>(false);

  const isNCRSynced = order?.ncr_sync_status === true;

  const fetchOrderDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axiosInstance.get(
        `api/pos/ncr-order-detail/${id}/`
      );
      if (response.status === 200) {
        const orderData = response.data;
        // console.log("orderdata:", orderData.order_details);
        setOrder(orderData);
        if (orderData.order_details?.items) {
          processProductDetails(orderData.order_details);
        }
      } else {
        setError("Failed to load order details.");
      }
    } catch (error) {
      console.error("Error fetching order details:", error);
      setError("Failed to load order details.");
    } finally {
      setLoading(false);
    }
  };

  const processProductDetails = (productDetails: ProductDetails) => {
    const CGST = productDetails.cgst;
    const SGST = productDetails.sgst;
    const items = productDetails.items;
    const formattedItems: ProcessedProduct[] = Object.values(items).map(
      (item: Item) => {
        const chooseSide = item.sections?.choose_side?.name || "";
        const chooseDrink = item.sections?.choose_drink?.name || "";

        // const modifiers = Object.entries(item.sections || {}).flatMap(
        //   ([_, section]: any) =>
        //     Object.entries(section.modifier_groups || {}).flatMap(
        //       ([__, group]: any) =>
        //         Object.entries(group).map(([___, value]: any) => ({
        //           code: value.code,
        //           modifier: value.name,
        //           quantity: value.quantity,
        //         }))
        //     )
        // );
        const modifiers = Object.entries(item.sections || {}).flatMap(
          ([_, section]: any) =>
            Object.entries(section.modifier_groups || {}).flatMap(
              ([__, group]: any) =>
                Object.entries(group)
                  .filter(([_, val]: [string, any]) => val?.code)
                  .map(([_, val]: [string, any]) => ({
                    code: val.code,
                    modifier: val.name,
                    quantity: val.quantity,
                  }))
            )
        );

        return {
          name: item.name,
          chooseSide: chooseSide,
          chooseDrink: chooseDrink,
          modifiers: modifiers,
          quantity: item.quantity,
          subTotal: item.sub_total,
          cgst: CGST,
          sgst: SGST,
        };
      }
    );

    setProductDetails(formattedItems);
  };

  const showJsonModal = useCallback(() => {
    //console.log("orders", order?.ncr_payload);

    if (order) {
      const formattedJson = JSON.stringify(order?.ncr_payload, null, 2);
      setJsonData(formattedJson);
      setModalTitle("NCR payload View Json");
      setIsModalVisible(true);
    }
  }, [order]);

  const showNCRResponseModal = useCallback(() => {
    // console.log("orders", order?.response_log);

    if (order) {
      const formattedJson = JSON.stringify(order?.response_log, null, 2);
      setJsonData(formattedJson);
      setModalTitle("NCR Response View Json");
      setIsModalVisible(true);
    }
  }, [order]);

  // const showJsonModal = () => {
  //   if (order) {
  //     const formattedJson = JSON.stringify(order, null, 2);

  //     setJsonData(formattedJson);
  //     setIsModalVisible(true);
  //   }
  // };

  const handleModalClose = useCallback(() => {
    setJsonData("");
    setModalTitle("");
    setCopied(false);
    setIsModalVisible(false);
  }, []);
  const handleCopy = useCallback(() => {
    if (jsonData) {
      message.success("JSON copied to clipboard!");
      setCopied(true);
      setTimeout(() => setCopied(false), 3000);
    } else {
      message.error("JSON data not found!");
    }
  }, [jsonData]);

  const handleNCRSyncRetry = async () => {
    try {
      setIsNCRSyncedLoading(true);
      setError(null);
      const response = await axiosInstance.post(`/api/pos/ncr-order-retry/`, {
        order_id: Number(id),
      });
      if (response.status === 200) {
        message.success(`${response.data.message}`);
        fetchOrderDetails();
      } else {
        setError("Failed to retry NCR Sync.");
      }
    } catch (error) {
      console.error("Error retrying NCR Sync:", error);
      setError("Failed to retry NCR Sync.");
    } finally {
      setIsNCRSyncedLoading(false);
    }
  };

  const onRetryClick = useCallback(() => {
    showConfirmActionModal({
      isActive: !isNCRSynced,
      onConfirm: handleNCRSyncRetry,
      Text: "Retry",
      entityName: "NCR Sync",
    });
  }, [handleNCRSyncRetry]);

  const formatValue = (text: string) => {
    if (!text) return "-";
    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const renderDetailsInCard = () => {
    if (!order) return null;

    const fields: { label: string; key: keyof OrderType }[] = [
      // { label: "Order ID", key: "id" },
      { label: "Store", key: "store" },
      { label: "Channel", key: "channel" },
      { label: "Customer Name", key: "customer_name" },
      { label: "Customer Phone", key: "customer_phone" },
      { label: "Table Number", key: "table_number" },
      { label: "Order Type", key: "order_type" },
      { label: "Payment Method", key: "payment_method" },
      { label: "Payment Status", key: "payment_status" },
      { label: "Order Status", key: "order_status" },
      { label: "Store ATO ID", key: "site_id" },
      { label: "Kiosk Terminal ID", key: "kiosk_terminal_id" },
      { label: "Created Date", key: "created_at" },
      { label: "Updated Date", key: "updated_at" },
      { label: "NCR Sync Status", key: "ncr_sync_status" },
      { label: "Is Order Processed", key: "is_order_processed" },
    ];

    return fields.map((field) => {
      const value = order[field.key];
      let displayValue: React.ReactNode;

      if (field.key === "created_at" && typeof value === "string") {
        displayValue = formatDate(value);
      } else if (field.key === "updated_at" && typeof value === "string") {
        displayValue = formatDate(value);
      } else if (typeof value === "boolean") {
        displayValue = value ? "Yes" : "No";
      } else if (value === null || value === undefined || value === "") {
        displayValue = "-";
      } else if (
        (field.key === "order_type" && typeof value === "string") ||
        (field.key === "payment_method" && typeof value === "string") ||
        (field.key === "payment_status" && typeof value === "string") ||
        (field.key === "order_status" && typeof value === "string")
      ) {
        displayValue = formatValue(value);
      } else if (field.key === "store" && typeof value === "string") {
        displayValue = (
          <Link
            className="common-link text-decoration-none"
            to={`/stores/${order.store_id}/details`}
          >
            {value}
          </Link>
        );
      } else if (typeof value === "object") {
        // Display a placeholder message for objects
        displayValue = "Details available";
      } else {
        displayValue = value;
      }

      return (
        <div
          key={field.key}
          className="order-details-value"
          style={{ marginBottom: "10px" }}
        >
          <div className="order-details-label" style={{ fontWeight: "bold" }}>
            {field.label}
          </div>
          <span
            className="order-details-value-colon"
            style={{ margin: "0 5px" }}
          >
            :
          </span>
          <span>{displayValue}</span>
        </div>
      );
    });
  };

  const formatDate = (dateString: string): string => {
    return dayjs(dateString).format("DD/MM/YYYY, hh:mm A");
  };

  useEffect(() => {
    fetchOrderDetails();
  }, [id]);

  if (loading) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ height: "100vh" }}
      >
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return <Alert message={error} type="error" />;
  }

  if (!order) {
    return <Alert message="Order not found." type="warning" />;
  }

  return (
    <div className="mt-2">
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div className="fw-bold fs-5">
          Order Detail <DoubleRightOutlined className="ms-2 me-2" />
          {order.order_id}
        </div>
        <div className="d-flex gap-2 flex-wrap">
          <Btn
            shape="round"
            className="btn-save"
            loading={isNCRSyncedLoading}
            disabled={isNCRSynced}
            onClick={onRetryClick}
          >
            {isNCRSynced ? "NCR Synced" : "Retry NCR Sync"}
          </Btn>
          <Btn
            shape="round"
            type="primary"
            className="btn-save mb-2"
            onClick={showJsonModal}
          >
            NCR Payload View Json
          </Btn>
          <Btn
            shape="round"
            type="primary"
            className="btn-save"
            onClick={showNCRResponseModal}
          >
            NCR Response View Json
          </Btn>
        </div>
      </div>

      <Modal
        title={
          <div
            // style={{
            //   display: "flex",
            //   justifyContent: "space-between",
            //   alignItems: "center",
            //   borderBottom: "2px solid #fa8431",
            //   paddingBottom: "10px",
            //   width: "100%",
            // }}
            className="modal-title"
          >
            <span>{modalTitle}</span>
            <div
              // style={{
              //   display: "flex",
              //   justifyContent: "center",
              //   marginRight: "20px",
              //   flexGrow: 1,
              // }}
              className="d-flex justify-content-center align-items-center flex-grow-1"
            >
              <CopyToClipboard key="copy" text={jsonData} onCopy={handleCopy}>
                <Btn
                  type="primary"
                  size="small"
                  style={copyButtonStyle(copied)}
                >
                  {copied ? "Copied" : "Copy JSON"}
                </Btn>
              </CopyToClipboard>
            </div>
          </div>
        }
        open={isModalVisible}
        maskClosable={false}
        onCancel={handleModalClose}
        footer={``}
        width={600}
      >
        <pre className="overflow-auto h-80 border-1 border-gray-300 p-2">
          {jsonData}
        </pre>
      </Modal>

      {/* <div className="d-flex">
          <div>
            <Button
              type="primary"
              icon={<PrinterOutlined />}
             
              className="mb-2"
            >
              Print Invoice
            </Button>
          </div>
        </div> */}

      <div className="order-details-card">{renderDetailsInCard()}</div>

      <div className="items-container">
        <h3 className="items-title">Items</h3>
        <table className="items-table">
          <thead>
            <tr className="items-table-header">
              <th>Name</th>
              <th>Modifiers</th>
              <th>Quantity</th>
              <th>Sub Total</th>
            </tr>
          </thead>
          <tbody>
            {productDetails.map((item: any, index: number) => (
              <tr key={index} className="items-table-row">
                <td>{item.name}</td>
                <td>
                  {item.modifiers.map((mod: any, modIndex: number) => (
                    <div key={modIndex}>
                      {mod.quantity} x {mod.code} ({mod.modifier})
                    </div>
                  ))}
                </td>
                <td>{item.quantity}</td>
                <td>{item.subTotal?.toFixed(2) || "-"}</td>
              </tr>
            ))}
          </tbody>
        </table>

        {/* Sub Total and Grand Total Section */}
        <div className="totals-container">
          <table className="totals-table">
            <tbody>
              <tr>
                <td className="totals-label">Sub Total:</td>
                <td className="totals-value">
                  {order.order_details?.sub_total?.toFixed(2) || "-"}
                </td>
              </tr>
              {/* <tr>
                <td className="totals-label">Discount Value:</td>
                <td className="totals-value">
                  {order.order_details?.sgst?.toFixed(2) || "0.00"}
                </td>
              </tr> */}
              {/* <tr>
                <td className="totals-label">Taxable Value:</td>
                <td className="totals-value">
                  {order.taxable_value?.toFixed(2) || "-"}
                </td>
              </tr> */}
              <tr>
                <td className="totals-label">SGST:</td>
                <td className="totals-value">
                  {productDetails[0].sgst?.toFixed(2) || "-"}
                </td>
              </tr>
              <tr>
                <td className="totals-label">CGST:</td>
                <td className="totals-value">
                  {productDetails[0].cgst?.toFixed(2) || "-"}
                </td>
              </tr>
              <tr>
                <td className="totals-grand-label">Grand Total:</td>
                <td className="totals-grand-value">
                  {order.order_details?.total?.toFixed(2) || "-"}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default OrderDetails;
