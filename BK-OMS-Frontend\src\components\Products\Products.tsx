import React, { useEffect, useState, useCallback, useMemo } from "react";
import "../Products/Products.css";
import { axiosInstance } from "../../apiCalls";
import { ProductDataProps } from "../../types";
import { message, Modal } from "antd";
import { useNavigate } from "react-router-dom";
import SearchBox from "../UI/SearchBox";
import { useTableFilters } from "../../customHooks/useFilter";
import DataTable from "../UI/DataTable/DataTable";
import CommonPagination from "../UI/Pagination/commonPagination";
import FilterButtons from "../UI/FilterButton";
import { handleApiError } from "../../utils/ApiErrorHandler";
import ErrorFallback from "../Error/ErrorPage";
import Link from "../UI/Link/index";
import Button from "../UI/Button";

export interface Product {
  objects: ProductDataProps[];
  page_size: number;
  current_page: number;
  total_pages: number;
  next_page: number | null;
  previous_page: number | null;
  total_count: number;
}

const Products: React.FC = () => {
  const {
    currentPage,
    pageSize,
    filters,
    appliedFilters,
    showClearButtons,
    handlePageChange,
    handleFilterChange,
    //clearAllFilters,
    clearFilter,
  } = useTableFilters();
  const navigate = useNavigate();
  const [products, setProducts] = useState<ProductDataProps[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  //const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [search, setSearch] = useState<string>("");

  const [error, setError] = useState<string | null>(null);

  //const inputRef = React.useRef<HTMLInputElement>(null);

  const memoizedFilters = useMemo(() => filters, [filters]);

  // const getProducts = useCallback(async () => {
  //   setLoading(true);
  //   try {
  //     const response = await axiosInstance.get(`/api/menu/products/`, {
  //       params: {
  //         page: currentPage,
  //         page_size: pageSize,
  //         ...memoizedFilters,
  //       },
  //     });
  //     if (response.status === 200) {
  //       setProducts(response.data);
  //       setTotalCount(response.data.total_count);
  //     } else {
  //       setProducts(null);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching menu data", error);
  //   } finally {
  //     setLoading(false);
  //   }
  // }, []);

  // useEffect(() => {
  //   getProducts();
  // }, [currentPage, pageSize, memoizedFilters]);

  useEffect(() => {
    const controller = new AbortController();
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await axiosInstance.get<Product>(
          "/api/menu/products/",
          {
            params: {
              page: currentPage,
              page_size: pageSize,
              ...memoizedFilters,
            },
            signal: controller.signal,
          }
        );

        if (response.status === 200) {
          setProducts(response.data.objects);
          setTotalCount(response.data.total_count);
          setError(null);
        } else {
          setError("Unexpected response format.");
        }
      } catch (error: unknown) {
        handleApiError(error, setError, navigate);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();

    return () => controller.abort();
  }, [currentPage, pageSize, memoizedFilters]);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const initialSearch = params.get("search") || "";
    setSearch(initialSearch);
  }, [location.search]);

  // useEffect(() => {
  //   // Retrieve the saved search value on component mount
  //   const savedSearch = localStorage.getItem("productSearch") || "";
  //   setSearch(savedSearch);
  // }, []);

  const syncProducts = useCallback(async () => {
    try {
      const response = await axiosInstance.post(
        `api/menu/sync-store-products/37f380ef-9ebd-4735-9fa3-cc62901835af/`
      );
      if (response.status === 200) {
        message.success(response.data.message);
      } else {
        message.error("Something went wrong");
      }
    } catch (error) {
      message.error("Something went wrong");
    }
  }, []);

  const handleSyncConfirmation = useCallback(() => {
    Modal.confirm({
      title: "Sync Products from NCR",
      content: "Are you sure you want to sync products from NCR?",
      okText: "Yes, Sync",
      cancelText: "No",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: syncProducts,
    });
  }, [syncProducts]);

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "search") {
      setSearch("");
      // localStorage.removeItem("productSearch");
    }
  };

  // const clearAllFiltersHandler = () => {
  //   clearAllFilters();
  //   setSearch("");
  //   //localStorage.removeItem("productSearch");
  // };

  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";

    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const columns = useMemo(
    () => [
      {
        title: "POS",
        width: "15%",
        dataIndex: "code",
        key: "code",
        fixed: "left" as "left",
        render: (text: string, record: ProductDataProps) => (
          <>
            {text ? (
              <Link
                className="common-link text-decoration-none"
                to={`details/${record.code}?name=${record.name}/`}
              >
                {text}
              </Link>
            ) : (
              "-"
            )}
          </>
        ),
      },
      {
        title: "Name",
        width: "15%",
        dataIndex: "name",
        key: "name",
      },
      {
        title: "Display Name",
        dataIndex: "display_name",
        key: "display_name",
        width: "15%",
      },
    ],
    [navigate]
  );

  const data = useMemo(
    () =>
      products.map((product) => ({
        key: product.id,
        ...product,
      })),
    [products]
  );

  // const handleSearch = useCallback(() => {
  //   if (search.trim() === "") {
  //     inputRef.current?.focus();
  //     return;
  //   }
  //   setCurrentPage(1);
  //   getProducts(1, search);
  // }, [search, getProducts]);
  const handleSearchChange = useCallback((value: string) => {
    //console.log(value);
    handleFilterChange("search", value);
    // localStorage.setItem("productSearch", value);
  }, []);

  // const handleClearSearch = useCallback(() => {
  //   setSearch("");
  //   handleFilterChange("search", "");
  // }, []);

  if (!loading && error) {
    // console.log("error", error);
    return (
      <>
        <ErrorFallback
          error={error}
          onClicked={() => window.location.reload()}
        />
      </>
    );
  }

  return (
    <div>
      <div className="main-dashboard-buttons">
        <Button className="typography" onClick={handleSyncConfirmation}>
          Sync Products From NCR
        </Button>
        <Link to={`./addproduct`}>
          <Button className="typography">+ Add New</Button>
        </Link>
      </div>
      <div className="container product-card-banner...........................">
        <div className="header products-headers d-flex flex-wrap justify-content-between align-items-center">
          <div className="title">PRODUCTS</div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            {/* <div className="search-container">
              <div className="button-serachs">
                <input
                  ref={inputRef}
                  className="search-text"
                  placeholder="Code or Name"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleSearch();
                    }
                  }}
                />
                <button onClick={handleSearch}>Search</button>

                {search && <button onClick={handleClearSearch}>Clear</button>}
              </div>
            </div> */}

            <div className="d-flex align-items-center flex-wrap">
              <div>
                <FilterButtons
                  showClearButtons={showClearButtons}
                  appliedFilters={appliedFilters}
                  //clearAllFilters={clearAllFiltersHandler}
                  ClearAllBtnClassName="clear-all-btn"
                  clearFilter={clearFilterHandler}
                  formatFilterValue={formatPaymentMethod}
                  filters={filters}
                  btnClassName="clear-btn"
                />
              </div>
              <div>
                <SearchBox
                  value={search}
                  onChange={setSearch}
                  onSearch={() => handleSearchChange(search)}
                  placeholder="Enter Code or Name"
                  // className="ms-2"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="pt-4 mt-4">
        <DataTable<ProductDataProps>
          columns={columns}
          dataSource={data}
          loading={loading}
          pagination={false}
          scroll={{ x: 1100, y: 700 }}
        />
      </div>
      <div className="d-flex justify-content-end align-items-center mt-4 mb-4">
        <CommonPagination
          current={currentPage}
          pageSize={pageSize}
          total={totalCount}
          showSizeChanger
          onShowSizeChange={handlePageChange}
          onChange={handlePageChange}
        />
      </div>
    </div>
  );
};

export default Products;
