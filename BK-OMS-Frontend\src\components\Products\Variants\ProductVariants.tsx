import { ProductVariantProps } from "../../../types/Products";
import LINK from "../../UI/Link/index";
import {  useParams } from "react-router-dom";
import StoreTabs from "../../../reusableComponents/store/StoreTabs";



const ProductVariants = () => {
  const { code } = useParams() as { code: string };
  // const { id } = useParams() as { id: string };

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "code",
      fixed: "left" as "left",
      width: "20%",
      render: (text: string, record: ProductVariantProps) => (
        // <span onClick={() => navigate(`/variants/${record.id}`)}>
        //   <Link>{text}</Link>
        // </span>
         <>
                 {text ? (
                      <LINK
                        className="common-link text-decoration-none"
                        to={`/variants/${record.id}/`}
                      >
                        {text}
                      </LINK>
                    ) : (
                      "-"
                    )}
                 </>
      ),
    },
    {
      title: "display name",
      dataIndex: "display_name",
      key: "display_name",
      width: "20%",
    },
    {
      title: "Id",
      dataIndex: "id",
      key: "id",
      width: "10%",
    },
    {
      title: "POS",
      dataIndex: "code",
      key: "code",
      width: "15%",
    },
    {
      title: "Availablity Status",
      dataIndex: "is_available",
      key: "is_available",
      width: "15%",
      render: (isActive: boolean) => (
        <div>{isActive ? "Yes" : "No"}</div>
      ),
    },
  ];
  const mapData = (response: any): ProductVariantProps[] =>
    response.objects.map((item: any) => ({
      key: item.id,
      id: item.id,
      name: item.name,
      display_name: item.display_name,
      code: item.code,
      description: item.description,
      product_type: item.product_type,
      base_product: item.base_product,
      is_available: item.is_available,
    }));

  return (
    <StoreTabs
      id={code}
      apiEndpoint="api/menu/v2/product-variants"
      name="Product Variants"
      columns={columns}
      dataMapper={mapData}
      // itemPath={itemPath}
      add={`/products/${code}/variants/add`}
    />
  );
};

export default ProductVariants;
