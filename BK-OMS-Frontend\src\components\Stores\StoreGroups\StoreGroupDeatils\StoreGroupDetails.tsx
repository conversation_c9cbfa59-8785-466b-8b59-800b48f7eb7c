import React, { useEffect, useState } from "react";
import { Card, Spin } from "antd";
import { EditOutlined } from "@ant-design/icons";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { StoreGroup } from "../StoreGroupsList/StoreGroupsList";
import { axiosInstance } from "../../../../apiCalls";
import BackButton from "../../../UI/BackButton";
import GroupMappedStores from "../GroupMappedStores/GroupMappedStores";
import Button from "../../../UI/Button";

const tabList = [
  { key: "Details", tab: "Details" },
  { key: "Group_Mapped_Stores", tab: "Mapped Stores" },
];

const StoreGroupDetails: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const activeTabKey1 = searchParams.get("tab") || "Details";

  const [storeDetails, setStoreDetails] = useState<StoreGroup | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchStore = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(`/api/stores/groups/${id}/`);
      if (response.status === 200) {
        setStoreDetails(response.data);
      } else {
        setStoreDetails(null);
      }
    } catch (error) {
      console.error("Error fetching menu data", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStore();
  }, [id]);

  const onTabChange = (key: string) => {
    setSearchParams({ tab: key });
  };

  const formatValue = (value: any): string => {
    if (value === undefined || value === null || value === "") return "-";
    if (typeof value === "boolean") return value ? "Yes" : "No";
    return value.toString();
  };

  const renderDetailsInCard = () => {
    if (!storeDetails) {
      return <div>No category data available.</div>;
    }

    const fields = [
      { key: "id", label: "ID" },
      { key: "name", label: "Name" },
      { key: "description", label: "Description" },
      { key: "is_active", label: "Active Status" },
    ];

    return fields.map((field) => (
      <div key={field.key} className="order-details-value">
        <div className="order-details-label">{field.label}</div>
        <span className="order-details-value-colon">:</span>
        <span className="order-details-value-value">
          {formatValue(storeDetails[field.key as keyof StoreGroup])}
        </span>
      </div>
    ));
  };

  const contentList: Record<string, React.ReactNode> = {
    Details: storeDetails ? (
      <div className="flex flex-col">
        <div className="d-flex justify-end">
          <Button
            className="typography text-white px-4 py-2 rounded transition"
            style={{ backgroundColor: "#FF8732" }}
            onMouseOver={(e) =>
              (e.currentTarget.style.backgroundColor = "#e86f1a")
            }
            onMouseOut={(e) =>
              (e.currentTarget.style.backgroundColor = "#FF8732")
            }
            onClick={() => navigate(`/edit-store-group/${id}`)}
          >
            <EditOutlined /> Edit
          </Button>
        </div>
        <div className="space-y-2">{renderDetailsInCard()}</div>
      </div>
    ) : (
      <p>No store details available.</p>
    ),
    Group_Mapped_Stores: (
      <div>
        <GroupMappedStores />
      </div>
    ),
  };

  return (
    <>
      {loading ? (
        <div className="d-flex justify-center align-items-center h-100">
          <Spin size="large" />
        </div>
      ) : (
        <>
          <div className="d-flex justify-between mb-2">
            <BackButton to="/store-groups" />
          </div>
          <Card
            style={{ width: "100%" }}
            title={null}
            tabList={tabList}
            activeTabKey={activeTabKey1}
            onTabChange={onTabChange}
          >
            {contentList[activeTabKey1]}
          </Card>
        </>
      )}
    </>
  );
};

export default StoreGroupDetails;
