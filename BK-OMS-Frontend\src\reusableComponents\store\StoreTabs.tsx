import { useState, useEffect, useCallback, useMemo } from "react";
import { axiosInstance } from "../../apiCalls";
import Link from "../../components/UI/Link";
import DataTable from "../../components/UI/DataTable/DataTable";
import SearchBox from "../../components/UI/SearchBox";
import { useTableFilters } from "../../customHooks/useFilter";

interface StoreTabsProps<T> {
  id: string;
  apiEndpoint: string;
  name: string;
  add?: string;
  columns: any[];
  dataMapper: (data: any) => T[];
  sendIdInBody?: boolean;
  refreshTrigger?: number;
}

const StoreTabs = <T extends { id: string | number }>({
  id,
  name,
  apiEndpoint,
  columns,
  dataMapper,
  add,
  sendIdInBody = false,
  refreshTrigger,
}: StoreTabsProps<T>) => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  //const [currentPage, setCurrentPage] = useState<number>(1);
  const [search, setSearch] = useState("");
  const [totalCount, setTotalCount] = useState<number>(0); // Store total count of items
  // const navigate = useNavigate();

  const {
    pageSize,
    currentPage,
    handlePageChange,
    handleFilterChange,
    filters,
  } = useTableFilters();

  const memoizedFilters = useMemo(() => filters, [filters]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const url = sendIdInBody ? apiEndpoint : `${apiEndpoint}/${id}/`;

      const requestOptions = sendIdInBody
        ? { page_size: pageSize, memoizedFilters, store_id: id } // Send store_id in body
        : { page: currentPage, memoizedFilters };

      const response = await axiosInstance.get(url, {
        params: requestOptions,
      });

      if (response.status === 200) {
        setData(dataMapper(response.data));
        setTotalCount(response.data.total_count);
      } else {
        setData([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching data", error);
      setData([]);
      setTotalCount(0);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, [currentPage, pageSize, memoizedFilters, refreshTrigger]);

  const handleSearchChange = useCallback((value: string) => {
    //console.log(value);
    handleFilterChange("search", value);
  }, []);

  const handleClearSearch = useCallback(() => {
    handleFilterChange("search", "");
    setSearch("");
  }, []);

  return (
    <div>
      {add && (
        <div className="main-dashboard-buttons">
          <Link to={add}>
            <button className="typography">+ Add New</button>
          </Link>
        </div>
      )}

      <div className="container product-card-banner">
        <div className="header products-headers">
          <div className="title">{name}</div>
          <div className="search-container">
            {/* <div className="button-serachs"> */}
            {/* <input
                className="search-text"
                placeholder="Search"
                onChange={(e) => setSearch(e.target.value)}
              />
              <button onClick={() => fetchData(currentPage, search)}>
                Search
              </button> */}
            <SearchBox
              value={search}
              onChange={setSearch}
              onSearch={() => handleSearchChange(search)}
              placeholder="Enter Code or Name"
              onClear={handleClearSearch}
              // className="ms-2"
            />
            {/* </div> */}
          </div>
        </div>
      </div>

      <div>
        <DataTable<T>
          columns={columns}
          dataSource={data}
          loading={loading}
          pagination={{
            current: currentPage,
            total: totalCount || 0,
            pageSize: pageSize,
            showSizeChanger: true,
            onChange: handlePageChange,
          }}
          scroll={{ x: 1100 }}
        />
      </div>
    </div>
  );
};

export default StoreTabs;
